New "Case" Page Design
The new/individual Case page under Immigration tab of the Client Dashboard will be a multi-step form integrated into the Irish Immigration Client Dashboard, allowing clients to submit new visa or preclearance applications efficiently. It will follow the dark blue and white design theme of the existing dashboard and adapt its grid-based layout to accommodate the required form fields. The form will dynamically adjust based on user inputs, ensuring a user-friendly experience while meeting all functional requirements.

Page Structure and Layout
The page will mirror the structure of the existing dashboard, with modifications to support a multi-step form process:

Left Sidebar:
Reuses the existing navigation design from the Client Dashboard.
Provides links to other dashboard sections 
Remains accessible throughout the form process for flexibility.
Header:
Spans the full width of the page.
Contains a progress indicator with five circles:
Step 1: Personal Information
Step 2: Visa/Preclearance Details
Step 3: Additional Information
Step 4: Document Upload
Step 5: Review and Submit

Circles are styled as follows:
Completed steps: Filled in green.
Current step: With blue fill.
Upcoming steps: Outlined in grey with white fill.

Transaction Information:
Displayed below the header (or in a fixed position).

Navigation Buttons:
Located at the bottom of each step.
Options: "Previous," "Next," and "Save and Continue Later."
On Step 5: "Previous" and "Submit Application" buttons.
Form Steps and Fields
The form is divided into five logical steps, with mandatory fields marked by a red asterisk (*) and dynamic fields that appear based on user selections (e.g., "Yes" responses triggering additional inputs). Below are the steps and their respective fields:

Step 1: Personal Information
Purpose: Collects basic personal details.
Fields:
Surname *
Forename *
Other Name
Date of Birth * (dd/mm/yyyy, with note: "Failure to give the correct date of birth may result in the application being invalid.")
Gender * (Dropdown: Male/Female)
Country of Birth *
Current Location *
Current Address *
Address Line 1 *
Address Line 2
Address Line 3
Address Line 4
Note: "A Post Office Box Address is not acceptable as a residential address. Failure to give a residential address will result in the application being invalid."
Contact Phone *
Contact Email *
Step 2: Visa/Preclearance Details
Purpose: Captures travel and passport information.
Fields:
Country of Nationality *
What is the reason for travel? *
What type of Visa/Preclearance are you applying for? * (Radio: Short Stay (C) / Long Stay (D))
Journey Type * (Radio: Single / Multiple)
Purpose of Travel *
Passport Type *
Passport/Travel Document Number *
Issuing Authority/Type *
Date of Issue * (dd/mm/yyyy)
Date of Expiry * (dd/mm/yyyy)
Proposed dates you wish to enter and leave Ireland *
From * (dd/mm/yyyy)
To * (dd/mm/yyyy)
Step 3: Additional Information
Purpose: Gathers supplementary details with dynamic field expansion.
Fields:
Length of residence in present country:
No of Years
No of Months
Do you have permission to return to that country after your stay in Ireland? (Radio: Yes/No)
Are you exempt from the requirement to provide biometrics? (Radio: Yes/No, with link: "Checklist of Biometric Exemptions")
Have you applied for an Irish Visa/Preclearance before? (Radio: Yes/No)
If Yes: Location, Transaction Number, Year of Issue
Have you ever been issued with an Irish Visa/Preclearance before? (Radio: Yes/No)
If Yes: Location, Transaction Number, Year of Issue
Have you ever been refused an Irish Visa/Preclearance? (Radio: Yes/No)
If Yes: Location of Application, Year, Reference Number
Have you ever been in Ireland before? (Radio: Yes/No)
Do you have family members living in Ireland? (Radio: Yes/No)
Have you ever been refused permission to enter Ireland before? (Radio: Yes/No)
Have you ever been notified of a deportation order to leave Ireland? (Radio: Yes/No)
Have you ever been refused a visa to another country? (Radio: Yes/No)
Have you ever been refused entry to, deported from, overstayed permission in, or were otherwise required to leave any country? (Radio: Yes/No)
If Yes: Details
Have you any criminal convictions in any country? (Radio: Yes/No)
Is this your first Passport? (Radio: Yes/No)
Are you currently employed in your country of residence? (Radio: Yes/No)
Are you currently a student in your country of residence? (Radio: Yes/No)
Will you be travelling with any other person? (Radio: Yes/No)
If Yes: Details (e.g., business colleague, family member, group)
Contact / Host in Ireland (Text input, optional)
Step 4: Document Upload
Purpose: Allows submission of supporting documents.
Fields:
File upload fields for required documents (e.g., passport scan, proof of residence).
Multiple slots with labels (e.g., "Passport," "Proof of Address") and a "Browse" button for each.
Step 5: Review and Submit
Purpose: Provides a final review before submission.
Content:
Summary of all entered data, grouped by section:
Personal Information
Visa/Preclearance Details
Additional Information
Uploaded Documents (list of file names)
Edit buttons next to each section to return to and modify previous steps.
Submit Application button to complete the process.
Design Theme and Styling
The page will adhere to the existing dashboard’s aesthetic:


Mandatory fields marked with a red asterisk (*).

Typography: Clean, sans-serif font consistent with the dashboard.
Accessibility: High-contrast colors, proper labels for screen readers, and keyboard navigation support.
Dynamic Functionality
Inspired by the reference link (https://www.visas.inis.gov.ie/avats/OnlineHome.aspx), the form will include dynamic behavior:

Conditional Fields:
Example: Selecting "Yes" to "Have you applied for an Irish Visa/Preclearance before?" reveals fields for Location, Transaction Number, and Year of Issue.
Similarly applied to other Yes/No questions with follow-up details.
Implementation Note: While this is a design task, the layout should indicate where dynamic fields appear (e.g., indented below triggering questions) to guide future development with JavaScript or similar.
Additional Features
Transaction Number:
Generated when the user starts a new case (post-purchase) and displayed prominently with the 30-day retrieval message.
Save Functionality:
"Save and Continue Later" button on each step saves progress as a draft, retrievable via the transaction number.
Validation:
Mandatory fields enforced before advancing (e.g., Surname, Date of Birth).
Format checks for dates (dd/mm/yyyy), email, and phone numbers (design should note these requirements).
Mapping to Existing Layout
While the existing layout shows all sections simultaneously in a grid (Personal Info, Case Info, Upload Documents, etc.), the new Case page adopts a stepped approach for usability with extensive fields. However, it retains:

The left sidebar for navigation.
The header with progress bar.
The dark blue, white text, black border theme.
The empty bottom-right space could be repurposed for the "Submit" button or summary in Step 5 if a single-page grid were preferred (though multi-step is recommended here).
Final Summary
The new Case page is a multi-step form tailored for submitting visa or preclearance applications within the Irish Immigration Client Dashboard. It features:


A five-step process (Personal Information, Visa/Preclearance Details, Additional Information, Document Upload, Review and Submit).
Dynamic fields that adapt to user inputs, mirroring the functionality of the Irish visa system.
A progress bar, transaction number display, and save functionality for user convenience.
This design ensures a professional, intuitive experience that aligns with the existing dashboard while meeting the specific needs of the visa application process.